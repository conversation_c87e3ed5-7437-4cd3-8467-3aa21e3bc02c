# Accumulated Trading Volume Task Security

## Overview

This document describes the security measures implemented to prevent unauthorized completion of accumulated trading volume tasks in the xbit-agent system.

## Security Vulnerability Fixed

### Previous Vulnerability

Users could bypass trading volume requirements by directly calling the `completeTask` GraphQL mutation with fabricated verification data:

```graphql
mutation CompleteTask {
    completeTask(
        input: {
            taskId: "a68d38c5-1578-48fd-974b-d08e3118afce"
            verificationData: "{\"volume\": 5.0, \"trade_type\": \"MEME\"}"
        }
    ) {
        success
        message
        pointsAwarded
    }
}
```

This would return a successful completion even though the user hadn't actually achieved the required trading volume.

### Root Cause

1. **Insufficient Task Type Detection**: Accumulated trading volume tasks were not properly identified and routed through the secure task registry system
2. **Generic Completion Path**: These tasks fell through to the generic `CompleteTask` method instead of the specialized `AccumulatedTradingHandler`
3. **Weak Verification**: The system accepted any volume data provided in verification data without validating against actual trading records

## Security Measures Implemented

### 1. Task Identification System

**File**: `internal/service/activity_cashback/task_management_service.go`

```go
// IsAccumulatedTradingVolumeTask checks if a task is an accumulated trading volume task
func (s *TaskManagementService) IsAccumulatedTradingVolumeTask(task *model.ActivityTask) bool {
    // Check required properties
    if task.ActionTarget == nil || *task.ActionTarget != "memeTrade" {
        return false
    }
    
    if task.Frequency != model.FrequencyProgressive {
        return false
    }
    
    if task.Conditions == nil || task.Conditions.MinTradingVolume == nil {
        return false
    }
    
    // Check identifier pattern
    if task.TaskIdentifier != nil {
        identifier := string(*task.TaskIdentifier)
        if len(identifier) > 23 && identifier[:23] == "ACCUMULATED_MEME_TRADING" {
            return true
        }
    }
    
    return true
}
```

### 2. Volume Verification Service

**File**: `internal/service/activity_cashback/task_management_service.go`

```go
// VerifyAccumulatedTradingVolume verifies actual trading volume from database
func (s *TaskManagementService) VerifyAccumulatedTradingVolume(ctx context.Context, userID uuid.UUID, task *model.ActivityTask) (bool, float64, error) {
    requiredVolume := *task.Conditions.MinTradingVolume
    
    // Get user's actual accumulated trading volume from database
    userTierInfo, err := s.tierService.GetUserTierInfo(ctx, userID)
    if err != nil {
        return false, 0, fmt.Errorf("failed to get user tier info: %w", err)
    }
    
    actualVolume := userTierInfo.GetEffectiveVolume().InexactFloat64()
    
    return actualVolume >= requiredVolume, actualVolume, nil
}
```

### 3. Security Validation in Task Completion

**File**: `internal/service/activity_cashback/task_management_service.go`

The `CompleteTask` method now includes comprehensive security checks:

```go
// SECURITY CHECK: Prevent manual completion of accumulated trading volume tasks
if s.IsAccumulatedTradingVolumeTask(task) {
    // Check for legitimate automated completion markers
    method, hasMethod := verificationData["method"].(string)
    processor, hasProcessor := verificationData["processor"].(string)
    source, hasSource := verificationData["source"].(string)
    
    isAutomatedCompletion := hasMethod && hasProcessor && hasSource &&
        method == "automated_tier_info_check" &&
        processor == "AccumulatedTradingHandler" &&
        source == "user_tier_info.effective_volume"
    
    if !isAutomatedCompletion {
        return fmt.Errorf("accumulated trading volume tasks can only be completed automatically when volume thresholds are met through legitimate trading activity")
    }
    
    // Verify actual volume against database
    volumeThresholdMet, actualVolume, err := s.VerifyAccumulatedTradingVolume(ctx, userID, task)
    if !volumeThresholdMet {
        return fmt.Errorf("trading volume threshold not met: required %.2f, actual %.2f", 
            *task.Conditions.MinTradingVolume, actualVolume)
    }
}
```

### 4. GraphQL Resolver Security

**File**: `internal/controller/graphql/resolvers/activity_cashback.go`

Updated the resolver to route accumulated trading volume tasks through the secure registry system:

```go
// Check if this task should use the task registry system
shouldUseRegistry := task.TaskIdentifier != nil && (
    *task.TaskIdentifier == model.TaskIDShareEarningsChart ||
    *task.TaskIdentifier == model.TaskIDConsecutiveCheckinConfigurable ||
    service.IsAccumulatedTradingVolumeTask(task))

if shouldUseRegistry {
    // Use task registry for tasks with specific handlers
    if err := service.ProcessTaskWithRegistry(ctx, userUUID, task, verificationData); err != nil {
        // Handle error
    }
} else {
    // Use traditional completion for other tasks
    if err := service.CompleteTask(ctx, userUUID, taskUUID, verificationData); err != nil {
        // Handle error
    }
}
```

## Legitimate Completion Flow

### Automated Completion Only

Accumulated trading volume tasks can ONLY be completed automatically through the following flow:

1. **Volume Sync**: The `RealtimeVolumeSyncTask` updates `user_tier_info.accumulated_volume_usd` based on actual trading transactions
2. **Task Processing**: The `AccumulatedTradingHandler` checks if volume thresholds are met
3. **Verification**: The handler verifies volume against database records
4. **Completion**: Task is completed with proper verification data markers

### Required Verification Data

For legitimate automated completion, verification data must include:

```go
verificationData := map[string]interface{}{
    "method":             "automated_tier_info_check",
    "processor":          "AccumulatedTradingHandler", 
    "source":             "user_tier_info.effective_volume",
    "milestone":          targetVolume,
    "accumulated_volume": actualVolume,
}
```

## Blocked Scenarios

The security measures block the following scenarios:

1. **Manual API Calls**: Direct calls to `completeTask` with fabricated data
2. **Fake NATS Events**: Attempts to simulate trading events
3. **Partial Markers**: Incomplete automated completion markers
4. **Wrong Processor**: Using incorrect processor names
5. **Volume Mismatch**: When actual volume doesn't meet requirements

## Testing

**File**: `internal/service/activity_cashback/task_security_test.go`

Comprehensive tests verify:

- Task identification logic
- Security validation
- Bypass attempt prevention
- Legitimate completion scenarios

Run tests with:
```bash
go test -v ./internal/service/activity_cashback -run TestTaskIdentifierPatterns
go test -v ./internal/service/activity_cashback -run TestAccumulatedTradingVolumeTaskIdentification
```

## Impact

### Security Benefits

1. **Prevents Fraud**: Users cannot fake trading volume to complete tasks
2. **Data Integrity**: Task completion reflects actual trading activity
3. **Fair System**: All users must meet legitimate volume requirements

### Backward Compatibility

- Existing legitimate automated completions continue to work
- No impact on other task types
- Admin functions remain unchanged

## Monitoring

The system logs all security events:

- Blocked manual completion attempts
- Volume verification results
- Legitimate automated completions

Monitor logs for patterns indicating attempted bypasses.
